<template>
  <section id="benefits-section" class="benefits-section py-5" role="region" aria-labelledby="benefits-title">
    <div class="container">
      <!-- Section Header -->
      <header class="text-center mb-5">
        <h2 id="benefits-title" class="section-title display-5 font-weight-bold mb-3">
          {{ benefitsData.title }}
        </h2>
        <p class="section-subtitle h5 text-muted mb-4">
          {{ benefitsData.subtitle }}
        </p>
        <p class="section-description lead text-muted">
          {{ benefitsData.description }}
        </p>
      </header>
      
      <!-- Benefits Grid -->
      <div class="row g-4">
        <div 
          v-for="(benefit, index) in benefitsData.items" 
          :key="index"
          class="col-lg-4 col-md-6"
        >
          <article class="benefit-card h-100">
            <div class="benefit-icon-wrapper">
              <div class="benefit-icon" :class="`benefit-icon-${index + 1}`">
                <fa :icon="getBenefitIcon(benefit.icon)" />
              </div>
            </div>
            
            <div class="benefit-content">
              <h3 class="benefit-title h5 font-weight-bold mb-3">
                {{ benefit.title }}
              </h3>
              
              <p class="benefit-description text-muted mb-3">
                {{ benefit.description }}
              </p>
              
              <ul class="benefit-features list-unstyled">
                <li v-for="(feature, fIndex) in benefit.features" :key="fIndex" class="benefit-feature">
                  <fa :icon="['fas', 'check']" class="feature-check text-success me-2" />
                  {{ feature }}
                </li>
              </ul>
            </div>
          </article>
        </div>
      </div>
      
      <!-- Comparison Section -->
      <div class="comparison-section mt-5 pt-5">
        <div class="row align-items-center">
          <div class="col-lg-6">
            <div class="comparison-content">
              <h3 class="comparison-title h3 font-weight-bold mb-4">
                {{ comparisonData.title }}
              </h3>
              <p class="comparison-description text-muted mb-4">
                {{ comparisonData.description }}
              </p>
              
              <div class="comparison-stats">
                <div class="row">
                  <div class="col-6 col-md-3" v-for="(stat, index) in comparisonData.stats" :key="index">
                    <div class="stat-item text-center">
                      <div class="stat-number h3 font-weight-bold text-primary">{{ stat.number }}</div>
                      <div class="stat-label small text-muted">{{ stat.label }}</div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
          
          <div class="col-lg-6">
            <div class="comparison-visual">
              <div class="comparison-table">
                <div class="table-header">
                  <div class="table-cell">{{ comparisonData.table.header.feature }}</div>
                  <div class="table-cell text-center">{{ comparisonData.table.header.us }}</div>
                  <div class="table-cell text-center">{{ comparisonData.table.header.others }}</div>
                </div>
                
                <div v-for="(row, index) in comparisonData.table.rows" :key="index" class="table-row">
                  <div class="table-cell">{{ row.feature }}</div>
                  <div class="table-cell text-center">
                    <fa :icon="['fas', 'check']" class="text-success" v-if="row.us" />
                    <fa :icon="['fas', 'times']" class="text-danger" v-else />
                  </div>
                  <div class="table-cell text-center">
                    <fa :icon="['fas', 'check']" class="text-success" v-if="row.others" />
                    <fa :icon="['fas', 'times']" class="text-danger" v-else />
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </section>
</template>

<script>
import { mapGetters } from 'vuex'

export default {
  name: 'BenefitsSection',
  
  computed: {
    ...mapGetters(['currentUIData']),
    
    benefitsData() {
      return this.currentUIData.sections?.benefits || {
        title: 'Why Choose Our Yu-Gi-Oh! Card Maker?',
        subtitle: 'The most advanced and user-friendly card creation tool',
        description: 'Discover why thousands of duelists trust our Yu-Gi-Oh! card maker for creating professional-quality custom cards.',
        items: [
          {
            title: 'Professional Quality',
            description: 'Create cards that look and feel like official Yu-Gi-Oh! cards with authentic templates and high-resolution output.',
            icon: 'star',
            features: ['Official card templates', 'High-resolution export', 'Authentic fonts and styling']
          },
          {
            title: 'Easy to Use',
            description: 'Intuitive interface designed for both beginners and experts. No design experience required.',
            icon: 'user-friendly',
            features: ['Drag & drop interface', 'Real-time preview', 'One-click download']
          },
          {
            title: 'Completely Free',
            description: 'All features are completely free with no hidden costs, watermarks, or registration requirements.',
            icon: 'free',
            features: ['No registration needed', 'No watermarks', 'Unlimited downloads']
          }
        ]
      }
    },
    
    comparisonData() {
      return this.currentUIData.sections?.comparison || {
        title: 'Compare with Other Tools',
        description: 'See how our Yu-Gi-Oh! card maker stands out from the competition.',
        stats: [
          { number: '100%', label: 'Free' },
          { number: '12', label: 'Languages' },
          { number: '∞', label: 'Downloads' },
          { number: '0', label: 'Watermarks' }
        ],
        table: {
          header: {
            feature: 'Feature',
            us: 'Our Tool',
            others: 'Others'
          },
          rows: [
            { feature: 'Completely Free', us: true, others: false },
            { feature: 'No Registration', us: true, others: false },
            { feature: 'No Watermarks', us: true, others: false },
            { feature: 'High Quality Export', us: true, others: true },
            { feature: 'Multi-language', us: true, others: false }
          ]
        }
      }
    }
  },
  
  methods: {
    /**
     * 獲取優勢圖標
     * @param {string} iconName - 圖標名稱
     * @returns {Array} Font Awesome 圖標數組
     */
    getBenefitIcon(iconName) {
      const iconMap = {
        star: ['fas', 'star'],
        'user-friendly': ['fas', 'user-friends'],
        free: ['fas', 'gift'],
        quality: ['fas', 'award'],
        speed: ['fas', 'bolt'],
        support: ['fas', 'headset']
      }
      
      return iconMap[iconName] || ['fas', 'check-circle']
    }
  }
}
</script>

<style scoped>
.benefits-section {
  background: #f8f9fa;
  position: relative;
}

.benefit-card {
  background: white;
  border-radius: 12px;
  padding: 2rem;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
  border: 1px solid rgba(0, 0, 0, 0.05);
}

.benefit-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
}

.benefit-icon-wrapper {
  margin-bottom: 1.5rem;
}

.benefit-icon {
  width: 60px;
  height: 60px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.5rem;
  color: white;
  margin: 0 auto;
}

.benefit-icon-1 {
  background: linear-gradient(45deg, #667eea, #764ba2);
}

.benefit-icon-2 {
  background: linear-gradient(45deg, #f093fb, #f5576c);
}

.benefit-icon-3 {
  background: linear-gradient(45deg, #4facfe, #00f2fe);
}

.benefit-feature {
  margin-bottom: 0.5rem;
  font-size: 0.9rem;
}

.feature-check {
  font-size: 0.8rem;
}

.comparison-section {
  border-top: 1px solid #e9ecef;
}

.stat-item {
  margin-bottom: 1rem;
}

.stat-number {
  margin-bottom: 0.25rem;
}

.comparison-table {
  background: white;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
}

.table-header {
  display: grid;
  grid-template-columns: 2fr 1fr 1fr;
  background: #f8f9fa;
  font-weight: 600;
}

.table-row {
  display: grid;
  grid-template-columns: 2fr 1fr 1fr;
  border-top: 1px solid #e9ecef;
}

.table-cell {
  padding: 1rem;
  display: flex;
  align-items: center;
}

@media (max-width: 768px) {
  .benefit-card {
    padding: 1.5rem;
  }
  
  .comparison-stats .row {
    text-align: center;
  }
  
  .comparison-table {
    font-size: 0.9rem;
  }
  
  .table-cell {
    padding: 0.75rem 0.5rem;
  }
}
</style>
