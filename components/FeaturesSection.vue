<template>
  <section id="features-section" class="features-section py-5" role="main">
    <div class="container">
      <!-- Section Header -->
      <header class="text-center mb-5">
        <h2 class="section-title display-5 font-weight-bold text-dark mb-3">
          {{ featuresData.title }}
        </h2>
        <p class="section-subtitle lead text-muted mb-4">
          {{ featuresData.subtitle }}
        </p>
        <div class="title-divider mx-auto"></div>
      </header>
      
      <!-- Features Grid -->
      <div class="row g-4">
        <div
          v-for="(feature, index) in featuresData.items"
          :key="index"
          class="col-lg-4 col-md-6"
        >
          <article class="feature-card h-100">
            <div class="feature-icon-wrapper">
              <div class="feature-icon">
                <fa :icon="getFeatureIcon(feature.icon)" />
              </div>
            </div>

            <div class="feature-content">
              <h3 class="feature-title h5 font-weight-bold mb-3">
                {{ feature.title }}
              </h3>

              <p class="feature-description text-muted mb-3">
                {{ feature.description }}
              </p>

              <!-- Feature Details -->
              <ul v-if="feature.details" class="feature-details list-unstyled mb-0">
                <li v-for="(detail, dIndex) in feature.details" :key="dIndex" class="feature-detail">
                  <fa :icon="['fas', 'check']" class="detail-check text-success me-2" />
                  <span class="detail-text">{{ detail }}</span>
                </li>
              </ul>
            </div>
          </article>
        </div>
      </div>
      
      <!-- Call to Action -->
      <div class="text-center mt-5">
        <button
          @click="scrollToEditor"
          class="btn btn-primary btn-lg"
          :aria-label="ctaText"
        >
          <fa :icon="['fas', 'rocket']" class="me-2" />
          {{ ctaText }}
        </button>
      </div>
    </div>
  </section>
</template>

<script>
import { mapGetters } from 'vuex'

export default {
  name: 'FeaturesSection',
  
  computed: {
    ...mapGetters(['currentUIData']),
    
    featuresData() {
      return this.currentUIData.sections?.features || {
        title: 'Powerful Yu-Gi-Oh! Card Maker Features',
        subtitle: 'Everything you need for professional Yu-Gi-Oh! card creation',
        description: 'Our comprehensive Yu-Gi-Oh! card maker provides all the tools and features you need to create authentic, professional-quality custom cards.',
        items: [
          {
            title: 'Complete Card Type Support',
            description: 'Create any type of Yu-Gi-Oh! card with full support for all official card types and subtypes.',
            icon: 'cards',
            details: [
              'Monster Cards: Normal, Effect, Fusion, Ritual, Synchro, Xyz, Link, Pendulum',
              'Spell Cards: Normal, Quick-Play, Continuous, Equip, Field, Ritual',
              'Trap Cards: Normal, Continuous, Counter',
              'Special card frames and backgrounds'
            ]
          },
          {
            title: 'Multi-Language Card Creation',
            description: 'Create Yu-Gi-Oh! cards in multiple languages with authentic fonts and layouts.',
            icon: 'globe',
            details: [
              '12 supported languages including English, Japanese, Chinese',
              'Authentic fonts for each language',
              'Proper text formatting and layout',
              'Cultural localization support'
            ]
          },
          {
            title: 'Professional Quality Output',
            description: 'Generate high-resolution Yu-Gi-Oh! cards with professional printing quality.',
            icon: 'download',
            details: [
              'High-resolution Canvas rendering',
              'Print-ready quality (300 DPI)',
              'Multiple export formats',
              'Instant download capability'
            ]
          },
          {
            title: 'Advanced Customization',
            description: 'Fine-tune every aspect of your Yu-Gi-Oh! card design with advanced options.',
            icon: 'magic',
            details: [
              'Custom card artwork upload',
              'Adjustable text sizes and positions',
              'Color customization options',
              'Foil stamp and rarity settings'
            ]
          },
          {
            title: 'Real-Time Preview',
            description: 'See your Yu-Gi-Oh! card changes instantly with our live preview system.',
            icon: 'eye',
            details: [
              'Instant visual feedback',
              'Interactive card preview',
              'Zoom and pan functionality',
              'Mobile-responsive design'
            ]
          },
          {
            title: 'User-Friendly Interface',
            description: 'Intuitive design tools that make Yu-Gi-Oh! card creation accessible to everyone.',
            icon: 'heart',
            details: [
              'Drag-and-drop image upload',
              'Step-by-step guidance',
              'Beginner-friendly controls',
              'Expert mode for advanced users'
            ]
          }
        ]
      }
    },
    
    ctaText() {
      return this.currentUIData.sections?.hero?.cta_button || '開始製作'
    }
  },
  
  methods: {
    /**
     * 獲取功能圖標
     * @param {string} iconName - 圖標名稱
     * @returns {Array} Font Awesome 圖標數組
     */
    getFeatureIcon(iconName) {
      const iconMap = {
        cards: ['fas', 'layer-group'],
        globe: ['fas', 'globe'],
        download: ['fas', 'download'],
        eye: ['fas', 'eye'],
        magic: ['fas', 'magic'],
        heart: ['fas', 'heart']
      }
      
      return iconMap[iconName] || ['fas', 'star']
    },
    
    /**
     * 滾動到編輯器區域
     */
    scrollToEditor() {
      const editorElement = document.getElementById('card-editor')
      if (editorElement) {
        editorElement.scrollIntoView({ 
          behavior: 'smooth',
          block: 'start'
        })
      }
    }
  }
}
</script>

<style scoped>
.features-section {
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  position: relative;
}

.features-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image: 
    radial-gradient(circle at 25% 25%, rgba(0, 123, 255, 0.05) 0%, transparent 50%),
    radial-gradient(circle at 75% 75%, rgba(108, 117, 125, 0.05) 0%, transparent 50%);
  pointer-events: none;
}

.container {
  position: relative;
  z-index: 1;
}

.section-title {
  color: #2c3e50;
  font-weight: 700;
  margin-bottom: 1rem;
}

.section-subtitle {
  color: #6c757d;
  font-size: 1.1rem;
  max-width: 600px;
  margin: 0 auto;
}

.title-divider {
  width: 80px;
  height: 4px;
  background: linear-gradient(45deg, #007bff, #6610f2);
  border-radius: 2px;
}

.feature-card {
  background: white;
  border-radius: 15px;
  padding: 2rem;
  text-align: center;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
  border: 1px solid rgba(0, 0, 0, 0.05);
  position: relative;
  overflow: hidden;
}

.feature-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(45deg, #007bff, #6610f2);
  transform: scaleX(0);
  transition: transform 0.3s ease;
}

.feature-card:hover {
  transform: translateY(-10px);
  box-shadow: 0 15px 35px rgba(0, 0, 0, 0.15);
}

.feature-card:hover::before {
  transform: scaleX(1);
}

.feature-icon-wrapper {
  margin-bottom: 1.5rem;
}

.feature-icon {
  width: 80px;
  height: 80px;
  background: linear-gradient(135deg, #007bff, #6610f2);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto;
  color: white;
  font-size: 2rem;
  transition: all 0.3s ease;
  position: relative;
}

.feature-icon::before {
  content: '';
  position: absolute;
  top: -5px;
  left: -5px;
  right: -5px;
  bottom: -5px;
  background: linear-gradient(135deg, #007bff, #6610f2);
  border-radius: 50%;
  opacity: 0;
  transition: opacity 0.3s ease;
  z-index: -1;
}

.feature-card:hover .feature-icon {
  transform: scale(1.1);
}

.feature-card:hover .feature-icon::before {
  opacity: 0.2;
}

.feature-title {
  color: #2c3e50;
  font-weight: 600;
  margin-bottom: 1rem;
}

.feature-description {
  color: #6c757d;
  line-height: 1.6;
  font-size: 0.95rem;
}

.feature-details {
  margin-top: 1rem;
  text-align: left;
}

.feature-detail {
  display: flex;
  align-items: flex-start;
  margin-bottom: 0.5rem;
  font-size: 0.85rem;
  line-height: 1.4;
}

.detail-check {
  font-size: 0.75rem;
  margin-top: 0.1rem;
  flex-shrink: 0;
}

.detail-text {
  color: #6c757d;
}

.btn-primary {
  background: linear-gradient(45deg, #007bff, #0056b3);
  border: none;
  padding: 1rem 2rem;
  font-weight: 600;
  border-radius: 50px;
  box-shadow: 0 4px 15px rgba(0, 123, 255, 0.4);
  transition: all 0.3s ease;
}

.btn-primary:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(0, 123, 255, 0.6);
}

/* Animation on scroll */
.feature-card {
  opacity: 0;
  transform: translateY(30px);
  animation: fadeInUp 0.6s ease forwards;
}

.feature-card:nth-child(1) { animation-delay: 0.1s; }
.feature-card:nth-child(2) { animation-delay: 0.2s; }
.feature-card:nth-child(3) { animation-delay: 0.3s; }
.feature-card:nth-child(4) { animation-delay: 0.4s; }
.feature-card:nth-child(5) { animation-delay: 0.5s; }
.feature-card:nth-child(6) { animation-delay: 0.6s; }

@keyframes fadeInUp {
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Responsive design */
@media (max-width: 768px) {
  .feature-card {
    padding: 1.5rem;
  }
  
  .feature-icon {
    width: 60px;
    height: 60px;
    font-size: 1.5rem;
  }
  
  .section-title {
    font-size: 2rem;
  }
  
  .btn-primary {
    padding: 0.75rem 1.5rem;
  }
}

@media (max-width: 576px) {
  .feature-card {
    padding: 1.25rem;
  }
  
  .section-title {
    font-size: 1.75rem;
  }
}

/* Accessibility */
@media (prefers-reduced-motion: reduce) {
  .feature-card,
  .feature-icon,
  .btn-primary {
    animation: none;
    transition: none;
  }
  
  .feature-card:hover {
    transform: none;
  }
}

/* High contrast mode */
@media (prefers-contrast: high) {
  .feature-card {
    border: 2px solid #000;
    background: #fff;
  }
  
  .feature-icon {
    background: #000;
    color: #fff;
  }
  
  .feature-title {
    color: #000;
  }
  
  .feature-description {
    color: #333;
  }
}

/* Focus styles for accessibility */
.feature-card:focus-within {
  outline: 2px solid #007bff;
  outline-offset: 2px;
}

.btn-primary:focus {
  outline: 2px solid #007bff;
  outline-offset: 2px;
}
</style>
