<template>
  <section id="testimonials-section" class="testimonials-section py-5" role="region" aria-labelledby="testimonials-title">
    <div class="container">
      <!-- Section Header -->
      <header class="text-center mb-5">
        <h2 id="testimonials-title" class="section-title display-5 font-weight-bold mb-3">
          {{ testimonialsData.title }}
        </h2>
        <p class="section-subtitle h5 text-muted mb-4">
          {{ testimonialsData.subtitle }}
        </p>
        <p class="section-description lead text-muted">
          {{ testimonialsData.description }}
        </p>
      </header>
      
      <!-- Testimonials Grid -->
      <div class="row g-4">
        <div 
          v-for="(testimonial, index) in testimonialsData.items" 
          :key="index"
          class="col-lg-4 col-md-6"
        >
          <article class="testimonial-card h-100">
            <div class="testimonial-content">
              <div class="testimonial-rating mb-3">
                <fa 
                  v-for="star in 5" 
                  :key="star"
                  :icon="['fas', 'star']" 
                  class="star-icon"
                  :class="{ 'star-filled': star <= testimonial.rating }"
                />
              </div>
              
              <blockquote class="testimonial-quote mb-4">
                "{{ testimonial.quote }}"
              </blockquote>
              
              <footer class="testimonial-author">
                <div class="author-avatar">
                  <img 
                    :src="testimonial.avatar || '/default-avatar.png'" 
                    :alt="`${testimonial.name} avatar`"
                    class="avatar-image"
                    @error="handleImageError"
                  />
                </div>
                <div class="author-info">
                  <cite class="author-name font-weight-bold">{{ testimonial.name }}</cite>
                  <div class="author-title text-muted small">{{ testimonial.title }}</div>
                  <div class="author-location text-muted small">{{ testimonial.location }}</div>
                </div>
              </footer>
            </div>
          </article>
        </div>
      </div>
      
      <!-- Social Proof Section -->
      <div class="social-proof-section mt-5 pt-5">
        <div class="row align-items-center">
          <div class="col-lg-6">
            <div class="social-proof-content">
              <h3 class="social-proof-title h3 font-weight-bold mb-4">
                {{ socialProofData.title }}
              </h3>
              <p class="social-proof-description text-muted mb-4">
                {{ socialProofData.description }}
              </p>
              
              <div class="social-proof-stats">
                <div class="row">
                  <div class="col-6 col-md-4" v-for="(stat, index) in socialProofData.stats" :key="index">
                    <div class="proof-stat text-center">
                      <div class="stat-number h2 font-weight-bold text-primary mb-1">{{ stat.number }}</div>
                      <div class="stat-label small text-muted">{{ stat.label }}</div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
          
          <div class="col-lg-6">
            <div class="community-showcase">
              <div class="showcase-grid">
                <div 
                  v-for="(showcase, index) in socialProofData.showcase" 
                  :key="index"
                  class="showcase-item"
                  :class="`showcase-item-${index + 1}`"
                >
                  <div class="showcase-content">
                    <fa :icon="getShowcaseIcon(showcase.type)" class="showcase-icon" />
                    <div class="showcase-text">
                      <div class="showcase-title">{{ showcase.title }}</div>
                      <div class="showcase-subtitle">{{ showcase.subtitle }}</div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      
      <!-- Call to Action -->
      <div class="testimonials-cta text-center mt-5">
        <h3 class="cta-title h4 font-weight-bold mb-3">
          {{ ctaData.title }}
        </h3>
        <p class="cta-description text-muted mb-4">
          {{ ctaData.description }}
        </p>
        <button 
          @click="scrollToEditor"
          class="btn btn-primary btn-lg"
          :aria-label="ctaData.button"
        >
          <fa :icon="['fas', 'play']" class="me-2" />
          {{ ctaData.button }}
        </button>
      </div>
    </div>
  </section>
</template>

<script>
import { mapGetters } from 'vuex'

export default {
  name: 'TestimonialsSection',
  
  computed: {
    ...mapGetters(['currentUIData']),
    
    testimonialsData() {
      return this.currentUIData.sections?.testimonials || {
        title: 'What Our Users Say',
        subtitle: 'Trusted by thousands of Yu-Gi-Oh! fans worldwide',
        description: 'See what duelists around the world are saying about our Yu-Gi-Oh! card maker.',
        items: [
          {
            quote: 'This is the best Yu-Gi-Oh! card maker I\'ve ever used. The quality is amazing and it\'s so easy to use!',
            name: 'Alex Chen',
            title: 'Tournament Player',
            location: 'California, USA',
            rating: 5,
            avatar: null
          },
          {
            quote: 'Perfect for creating custom cards for my deck. The templates are authentic and the export quality is professional.',
            name: 'Maria Rodriguez',
            title: 'Card Collector',
            location: 'Madrid, Spain',
            rating: 5,
            avatar: null
          },
          {
            quote: 'I love how I can create cards in multiple languages. Great tool for international tournaments!',
            name: 'Hiroshi Tanaka',
            title: 'Duelist',
            location: 'Tokyo, Japan',
            rating: 5,
            avatar: null
          }
        ]
      }
    },
    
    socialProofData() {
      return this.currentUIData.sections?.social_proof || {
        title: 'Join Our Growing Community',
        description: 'Thousands of duelists worldwide trust our Yu-Gi-Oh! card maker for their custom card needs.',
        stats: [
          { number: '50K+', label: 'Cards Created' },
          { number: '12', label: 'Languages' },
          { number: '100+', label: 'Countries' }
        ],
        showcase: [
          { type: 'community', title: 'Active Community', subtitle: 'Join discussions' },
          { type: 'updates', title: 'Regular Updates', subtitle: 'New features' },
          { type: 'support', title: '24/7 Support', subtitle: 'Always here' }
        ]
      }
    },
    
    ctaData() {
      return this.currentUIData.sections?.testimonials_cta || {
        title: 'Ready to Create Your Cards?',
        description: 'Join thousands of satisfied users and start creating professional Yu-Gi-Oh! cards today.',
        button: 'Start Creating Now'
      }
    }
  },
  
  methods: {
    /**
     * 處理圖片載入錯誤
     */
    handleImageError(event) {
      // 使用默認頭像或生成字母頭像
      event.target.style.display = 'none'
      const parent = event.target.parentElement
      if (parent) {
        parent.innerHTML = '<div class="default-avatar">👤</div>'
      }
    },
    
    /**
     * 獲取展示圖標
     */
    getShowcaseIcon(type) {
      const iconMap = {
        community: ['fas', 'users'],
        updates: ['fas', 'sync-alt'],
        support: ['fas', 'headset']
      }
      return iconMap[type] || ['fas', 'star']
    },
    
    /**
     * 滾動到編輯器區域
     */
    scrollToEditor() {
      const editorElement = document.getElementById('card-editor')
      if (editorElement) {
        editorElement.scrollIntoView({ 
          behavior: 'smooth',
          block: 'start'
        })
      }
    }
  }
}
</script>

<style scoped>
.testimonials-section {
  background: white;
  position: relative;
}

.testimonial-card {
  background: #f8f9fa;
  border-radius: 12px;
  padding: 2rem;
  transition: all 0.3s ease;
  border: 1px solid rgba(0, 0, 0, 0.05);
}

.testimonial-card:hover {
  transform: translateY(-3px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

.testimonial-rating {
  display: flex;
  gap: 0.25rem;
}

.star-icon {
  color: #ddd;
  font-size: 1rem;
}

.star-filled {
  color: #ffc107;
}

.testimonial-quote {
  font-style: italic;
  font-size: 1.1rem;
  line-height: 1.6;
  color: #333;
}

.testimonial-author {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.author-avatar {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  overflow: hidden;
  background: #e9ecef;
  display: flex;
  align-items: center;
  justify-content: center;
}

.avatar-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.default-avatar {
  font-size: 1.5rem;
  color: #6c757d;
}

.author-name {
  display: block;
  margin-bottom: 0.25rem;
}

.social-proof-section {
  border-top: 1px solid #e9ecef;
}

.proof-stat {
  margin-bottom: 1.5rem;
}

.showcase-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1rem;
}

.showcase-item {
  background: white;
  border-radius: 8px;
  padding: 1.5rem;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
}

.showcase-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.showcase-content {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.showcase-icon {
  font-size: 1.5rem;
  color: #667eea;
}

.showcase-title {
  font-weight: 600;
  margin-bottom: 0.25rem;
}

.showcase-subtitle {
  font-size: 0.9rem;
  color: #6c757d;
}

.testimonials-cta {
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border-radius: 12px;
  padding: 3rem 2rem;
}

@media (max-width: 768px) {
  .testimonial-card {
    padding: 1.5rem;
  }
  
  .showcase-grid {
    grid-template-columns: 1fr;
  }
  
  .testimonials-cta {
    padding: 2rem 1rem;
  }
}
</style>
