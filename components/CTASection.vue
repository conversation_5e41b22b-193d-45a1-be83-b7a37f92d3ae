<template>
  <section class="cta-section py-5" role="region" aria-labelledby="cta-title">
    <div class="cta-background">
      <div class="cta-overlay"></div>
    </div>
    
    <div class="container">
      <div class="row justify-content-center">
        <div class="col-lg-8 col-md-10 text-center">
          <div class="cta-content">
            <h2 id="cta-title" class="cta-title display-5 font-weight-bold text-white mb-4">
              {{ ctaData.title }}
            </h2>
            
            <p class="cta-subtitle h5 text-light mb-4">
              {{ ctaData.subtitle }}
            </p>
            
            <p class="cta-description lead text-light mb-5">
              {{ ctaData.description }}
            </p>
            
            <div class="cta-actions">
              <button 
                @click="scrollToEditor"
                class="btn btn-primary btn-lg me-3 mb-3"
                :aria-label="ctaData.primary_button"
              >
                <fa :icon="['fas', 'rocket']" class="me-2" />
                {{ ctaData.primary_button }}
              </button>
              
              <button 
                @click="scrollToFeatures"
                class="btn btn-outline-light btn-lg mb-3"
                :aria-label="ctaData.secondary_button"
              >
                <fa :icon="['fas', 'info-circle']" class="me-2" />
                {{ ctaData.secondary_button }}
              </button>
            </div>
            
            <!-- Trust indicators -->
            <div class="trust-indicators mt-5">
              <div class="row justify-content-center align-items-center">
                <div class="col-auto">
                  <div class="trust-item">
                    <fa :icon="['fas', 'check-circle']" class="trust-icon text-success me-2" />
                    <span class="trust-text">{{ ctaData.trust_free }}</span>
                  </div>
                </div>
                <div class="col-auto">
                  <div class="trust-item">
                    <fa :icon="['fas', 'shield-alt']" class="trust-icon text-success me-2" />
                    <span class="trust-text">{{ ctaData.trust_secure }}</span>
                  </div>
                </div>
                <div class="col-auto">
                  <div class="trust-item">
                    <fa :icon="['fas', 'clock']" class="trust-icon text-success me-2" />
                    <span class="trust-text">{{ ctaData.trust_instant }}</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </section>
</template>

<script>
import { mapGetters } from 'vuex'

export default {
  name: 'CTASection',
  
  computed: {
    ...mapGetters(['currentUIData']),
    
    ctaData() {
      return this.currentUIData.sections?.cta || {
        title: 'Ready to Create Your Yu-Gi-Oh! Cards?',
        subtitle: 'Join thousands of duelists creating custom cards',
        description: 'Start designing professional Yu-Gi-Oh! cards right now. No registration required, completely free to use.',
        primary_button: 'Start Creating Now',
        secondary_button: 'Learn More',
        trust_free: 'Completely Free',
        trust_secure: 'Safe & Secure',
        trust_instant: 'Instant Access'
      }
    }
  },
  
  methods: {
    /**
     * 滾動到編輯器區域
     */
    scrollToEditor() {
      const editorElement = document.getElementById('card-editor')
      if (editorElement) {
        editorElement.scrollIntoView({ 
          behavior: 'smooth',
          block: 'start'
        })
      }
    },
    
    /**
     * 滾動到功能介紹區域
     */
    scrollToFeatures() {
      const featuresElement = document.getElementById('features-section')
      if (featuresElement) {
        featuresElement.scrollIntoView({ 
          behavior: 'smooth',
          block: 'start'
        })
      }
    }
  }
}
</script>

<style scoped>
.cta-section {
  position: relative;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  overflow: hidden;
}

.cta-background {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image: 
    radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.3) 0%, transparent 50%),
    radial-gradient(circle at 80% 20%, rgba(255, 119, 198, 0.3) 0%, transparent 50%);
}

.cta-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.2);
}

.cta-content {
  position: relative;
  z-index: 2;
}

.cta-title {
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.cta-subtitle,
.cta-description {
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
}

.btn-primary {
  background: linear-gradient(45deg, #ff6b6b, #ee5a24);
  border: none;
  box-shadow: 0 4px 15px rgba(255, 107, 107, 0.4);
  transition: all 0.3s ease;
}

.btn-primary:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(255, 107, 107, 0.6);
}

.btn-outline-light {
  border: 2px solid rgba(255, 255, 255, 0.8);
  transition: all 0.3s ease;
}

.btn-outline-light:hover {
  background: rgba(255, 255, 255, 0.1);
  border-color: white;
  transform: translateY(-2px);
}

.trust-indicators {
  opacity: 0.9;
}

.trust-item {
  display: flex;
  align-items: center;
  margin: 0 1rem;
  font-size: 0.9rem;
  color: rgba(255, 255, 255, 0.9);
}

.trust-icon {
  font-size: 1.1rem;
}

@media (max-width: 768px) {
  .cta-title {
    font-size: 2rem;
  }
  
  .trust-item {
    margin: 0.5rem 0;
    justify-content: center;
  }
  
  .trust-indicators .row {
    flex-direction: column;
  }
}
</style>
